import { useState } from "react";
import { useSearchFilter } from "@/contexts/SearchFilterContext";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MapPin, Star, Heart, ExternalLink } from "lucide-react";
import { Link } from "react-router-dom";
import { LeafletMap } from "./LeafletMap";

// Mock map coordinates for San Francisco areas
const locationCoordinates: Record<string, { lat: number; lng: number }> = {
  'Downtown': { lat: 37.7749, lng: -122.4194 },
  'Mission District': { lat: 37.7599, lng: -122.4148 },
  'Marina': { lat: 37.8021, lng: -122.4364 },
  'SoMa': { lat: 37.7749, lng: -122.4094 },
  'Castro District': { lat: 37.7609, lng: -122.4350 },
  'Richmond': { lat: 37.7806, lng: -122.4644 },
  'North Beach': { lat: 37.8067, lng: -122.4103 },
  'Sunset': { lat: 37.7431, lng: -122.4697 },
  'Financial District': { lat: 37.7946, lng: -122.4014 },
};

export const MapView = () => {
  const { filteredSalons } = useSearchFilter();
  const [selectedSalon, setSelectedSalon] = useState<string | null>(null);

  // Convert salon data to include coordinates
  const salonsWithCoords = filteredSalons.map(salon => ({
    ...salon,
    coordinates: salon.coordinates || locationCoordinates[salon.location] || locationCoordinates['Downtown'],
    price: Math.round(salon.rating * 20), // Mock price calculation
  }));

  const handleSalonClick = (salonId: string) => {
    setSelectedSalon(selectedSalon === salonId ? null : salonId);
  };

  return (
    <div className="relative">
      <LeafletMap
        salons={salonsWithCoords}
        selectedSalon={selectedSalon}
        onSalonSelect={handleSalonClick}
        center={{ lat: 37.7749, lng: -122.4194 }}
        zoom={13}
        height="600px"
      />

      {/* Selected Salon Details */}
      {selectedSalon && (
        <div className="absolute bottom-4 left-4 right-4 z-30">
          {(() => {
            const salon = salonsWithCoords.find(s => s.id === selectedSalon);
            if (!salon) return null;

            return (
              <Card className="bg-white shadow-lg">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg text-glamspot-neutral-900">{salon.name}</h3>
                      <div className="flex items-center gap-2 mt-1">
                        <MapPin className="w-4 h-4 text-glamspot-neutral-500" />
                        <span className="text-sm text-glamspot-neutral-600">{salon.location}</span>
                      </div>
                      <div className="flex items-center gap-4 mt-2">
                        <div className="flex items-center gap-1">
                          <Star className="w-4 h-4 text-yellow-500 fill-current" />
                          <span className="text-sm font-medium">{salon.rating}</span>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          ${salon.price}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline" asChild>
                        <Link to={`/salon/${salon.id}`}>
                          <ExternalLink className="w-4 h-4 mr-1" />
                          View
                        </Link>
                      </Button>
                      <Button size="sm" variant="ghost">
                        <Heart className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })()}
        </div>
      )}
    </div>
  );
};
