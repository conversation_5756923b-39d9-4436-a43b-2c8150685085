import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Search, 
  MoreHorizontal, 
  Eye, 
  Check, 
  X, 
  UserCheck,
  Building2,
  Mail,
  Phone,
  MapPin,
  Clock
} from 'lucide-react';
import { SalonRegistrationRequest } from '@/types';
import { SalonRequestService } from '@/services/salonRequestService';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/components/ui/sonner';

const SalonRequests = () => {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedRequest, setSelectedRequest] = useState<SalonRegistrationRequest | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectionDialog, setShowRejectionDialog] = useState(false);
  const [requestToReject, setRequestToReject] = useState<SalonRegistrationRequest | null>(null);
  const [requests, setRequests] = useState<SalonRegistrationRequest[]>([]);
  const [loading, setLoading] = useState(true);

  // Load salon requests from Firebase
  useEffect(() => {
    const loadRequests = async () => {
      if (!user || user.role !== 'admin') return;

      try {
        setLoading(true);
        const requestsData = await SalonRequestService.getAllSalonRequests();
        setRequests(requestsData);
      } catch (error) {
        console.error('Error loading salon requests:', error);
        toast.error('Failed to load salon requests');
      } finally {
        setLoading(false);
      }
    };

    loadRequests();

    // Set up real-time listener for salon requests
    const unsubscribe = SalonRequestService.onSalonRequestsChange((requestsData) => {
      setRequests(requestsData);
    });

    return () => unsubscribe();
  }, [user]);

  const filteredRequests = requests.filter(request => {
    const matchesSearch = 
      request.ownerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.salonName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.ownerEmail.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || request.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const handleViewRequest = (request: SalonRegistrationRequest) => {
    setSelectedRequest(request);
  };

  const handleApproveRequest = async (request: SalonRegistrationRequest) => {
    try {
      await SalonRequestService.approveSalonRequest(request.id, user?.id || 'admin');
      toast.success(`Request from ${request.ownerName} has been approved`);
      setSelectedRequest(null);
    } catch (error) {
      console.error('Error approving request:', error);
      toast.error('Failed to approve request. Please try again.');
    }
  };

  const handleRejectRequest = (request: SalonRegistrationRequest) => {
    setRequestToReject(request);
    setShowRejectionDialog(true);
  };

  const confirmRejection = async () => {
    if (requestToReject && rejectionReason.trim()) {
      try {
        await SalonRequestService.rejectSalonRequest(
          requestToReject.id,
          user?.id || 'admin',
          rejectionReason
        );
        toast.success(`Request from ${requestToReject.ownerName} has been rejected`);
        setShowRejectionDialog(false);
        setRequestToReject(null);
        setRejectionReason('');
        setSelectedRequest(null);
      } catch (error) {
        console.error('Error rejecting request:', error);
        toast.error('Failed to reject request. Please try again.');
      }
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800',
    };
    
    return (
      <Badge className={variants[status as keyof typeof variants] || ''}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const pendingCount = requests.filter(r => r.status === 'pending').length;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-glamspot-primary mx-auto"></div>
          <p className="mt-2 text-glamspot-neutral-600">Loading salon requests...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-glamspot-neutral-900">Salon Requests</h1>
          <p className="text-glamspot-neutral-600 mt-2">
            Review and manage salon registration requests
          </p>
        </div>
        {pendingCount > 0 && (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-300">
            {pendingCount} pending request{pendingCount !== 1 ? 's' : ''}
          </Badge>
        )}
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-glamspot-neutral-400 w-4 h-4" />
              <Input
                placeholder="Search by owner name, salon name, or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
            <Badge variant="outline" className="text-glamspot-neutral-600">
              {filteredRequests.length} request{filteredRequests.length !== 1 ? 's' : ''}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Requests Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCheck className="w-5 h-5 text-glamspot-primary" />
            All Requests
          </CardTitle>
          <CardDescription>
            Complete list of all salon registration requests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Owner</TableHead>
                <TableHead>Salon</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRequests.map((request) => (
                <TableRow key={request.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-glamspot-neutral-200 rounded-full flex items-center justify-center">
                        <UserCheck className="w-5 h-5 text-glamspot-neutral-600" />
                      </div>
                      <div>
                        <p className="font-medium text-glamspot-neutral-900">{request.ownerName}</p>
                        <div className="flex items-center gap-1 text-sm text-glamspot-neutral-500">
                          <Mail className="w-3 h-3" />
                          {request.ownerEmail}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium text-glamspot-neutral-900">{request.salonName}</p>
                      <div className="flex items-center gap-1 text-sm text-glamspot-neutral-500">
                        <MapPin className="w-3 h-3" />
                        <span className="truncate max-w-xs">{request.salonAddress}</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1 text-sm text-glamspot-neutral-600">
                      <Clock className="w-4 h-4" />
                      {new Date(request.submittedAt).toLocaleDateString()}
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(request.status)}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewRequest(request)}>
                          <Eye className="w-4 h-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        {request.status === 'pending' && (
                          <>
                            <DropdownMenuItem onClick={() => handleApproveRequest(request)}>
                              <Check className="w-4 h-4 mr-2" />
                              Approve Request
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleRejectRequest(request)}
                              className="text-red-600"
                            >
                              <X className="w-4 h-4 mr-2" />
                              Reject Request
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Request Details Dialog */}
      <Dialog open={!!selectedRequest} onOpenChange={() => setSelectedRequest(null)}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Salon Registration Request</DialogTitle>
            <DialogDescription>Complete details of the registration request</DialogDescription>
          </DialogHeader>
          {selectedRequest && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-glamspot-neutral-700">Owner Information</label>
                    <div className="mt-2 space-y-2">
                      <p className="text-glamspot-neutral-900 font-medium">{selectedRequest.ownerName}</p>
                      <div className="flex items-center gap-2">
                        <Mail className="w-4 h-4 text-glamspot-neutral-500" />
                        <span className="text-sm text-glamspot-neutral-600">{selectedRequest.ownerEmail}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="w-4 h-4 text-glamspot-neutral-500" />
                        <span className="text-sm text-glamspot-neutral-600">{selectedRequest.ownerPhone}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-glamspot-neutral-700">Business License</label>
                    <p className="text-glamspot-neutral-900 mt-1">{selectedRequest.businessLicense || 'Not provided'}</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-glamspot-neutral-700">Salon Information</label>
                    <div className="mt-2 space-y-2">
                      <p className="text-glamspot-neutral-900 font-medium">{selectedRequest.salonName}</p>
                      <div className="flex items-start gap-2">
                        <MapPin className="w-4 h-4 text-glamspot-neutral-500 mt-0.5" />
                        <span className="text-sm text-glamspot-neutral-600">{selectedRequest.salonAddress}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-glamspot-neutral-700">Status</label>
                    <div className="mt-1">
                      {getStatusBadge(selectedRequest.status)}
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-glamspot-neutral-700">Salon Description</label>
                <p className="text-glamspot-neutral-900 mt-1">{selectedRequest.salonDescription}</p>
              </div>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="text-sm font-medium text-glamspot-neutral-700">Submitted</label>
                  <p className="text-glamspot-neutral-900 mt-1">
                    {new Date(selectedRequest.submittedAt).toLocaleDateString()}
                  </p>
                </div>
                {selectedRequest.reviewedAt && (
                  <div>
                    <label className="text-sm font-medium text-glamspot-neutral-700">Reviewed</label>
                    <p className="text-glamspot-neutral-900 mt-1">
                      {new Date(selectedRequest.reviewedAt).toLocaleDateString()}
                    </p>
                  </div>
                )}
              </div>
              {selectedRequest.rejectionReason && (
                <div>
                  <label className="text-sm font-medium text-glamspot-neutral-700">Rejection Reason</label>
                  <p className="text-red-600 mt-1">{selectedRequest.rejectionReason}</p>
                </div>
              )}
              {selectedRequest.status === 'pending' && (
                <div className="flex justify-end gap-3 pt-4 border-t">
                  <Button
                    onClick={() => handleApproveRequest(selectedRequest)}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <Check className="w-4 h-4 mr-2" />
                    Approve Request
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={() => handleRejectRequest(selectedRequest)}
                  >
                    <X className="w-4 h-4 mr-2" />
                    Reject Request
                  </Button>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Rejection Dialog */}
      <AlertDialog open={showRejectionDialog} onOpenChange={setShowRejectionDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Reject Salon Request</AlertDialogTitle>
            <AlertDialogDescription>
              Please provide a reason for rejecting this salon registration request.
              This will be sent to the salon owner.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="Enter rejection reason..."
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setRejectionReason('');
              setRequestToReject(null);
            }}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmRejection}
              disabled={!rejectionReason.trim()}
              className="bg-red-600 hover:bg-red-700"
            >
              Reject Request
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default SalonRequests;
