import React, { useEffect, useRef } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface Salon {
  id: string;
  name: string;
  location: string;
  coordinates?: { lat: number; lng: number };
  rating: number;
  price?: number;
}

interface LeafletMapProps {
  salons: Salon[];
  selectedSalon?: string | null;
  onSalonSelect?: (salonId: string) => void;
  center?: { lat: number; lng: number };
  zoom?: number;
  height?: string;
}

export const LeafletMap: React.FC<LeafletMapProps> = ({
  salons,
  selectedSalon,
  onSalonSelect,
  center = { lat: 37.7749, lng: -122.4194 }, // San Francisco default
  zoom = 13,
  height = '600px'
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  const markersRef = useRef<L.Marker[]>([]);

  useEffect(() => {
    if (!mapRef.current) return;

    // Initialize map
    mapInstanceRef.current = L.map(mapRef.current).setView([center.lat, center.lng], zoom);

    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors'
    }).addTo(mapInstanceRef.current);

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [center.lat, center.lng, zoom]);

  useEffect(() => {
    if (!mapInstanceRef.current) return;

    // Clear existing markers
    markersRef.current.forEach(marker => {
      mapInstanceRef.current?.removeLayer(marker);
    });
    markersRef.current = [];

    // Add markers for salons
    salons.forEach((salon) => {
      if (!salon.coordinates) return;

      const marker = L.marker([salon.coordinates.lat, salon.coordinates.lng])
        .addTo(mapInstanceRef.current!)
        .bindPopup(`
          <div class="p-2">
            <h3 class="font-semibold text-lg">${salon.name}</h3>
            <p class="text-sm text-gray-600">${salon.location}</p>
            <div class="flex items-center mt-1">
              <span class="text-yellow-500">★</span>
              <span class="ml-1 text-sm">${salon.rating}</span>
              ${salon.price ? `<span class="ml-2 text-sm font-medium">$${salon.price}</span>` : ''}
            </div>
          </div>
        `);

      if (onSalonSelect) {
        marker.on('click', () => {
          onSalonSelect(salon.id);
        });
      }

      // Highlight selected salon
      if (selectedSalon === salon.id) {
        marker.openPopup();
      }

      markersRef.current.push(marker);
    });
  }, [salons, selectedSalon, onSalonSelect]);

  return (
    <div 
      ref={mapRef} 
      style={{ height, width: '100%' }}
      className="rounded-lg overflow-hidden border border-glamspot-neutral-200"
    />
  );
};
