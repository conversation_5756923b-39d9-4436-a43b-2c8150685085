import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  Building2,
  MapPin,
  Phone,
  Mail,
  Clock,
  Star,
  Camera,
  Save,
  Edit,
  Globe,
  Instagram,
  Facebook
} from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { LocationPicker } from '@/components/LocationPicker';
import { Salon } from '@/types';
import { SalonService } from '@/services/salonService';
import { InlineLoader } from '@/components/ui/loading-spinner';

const SalonOwnerProfile = () => {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [isLocationPickerOpen, setIsLocationPickerOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  // Salon data from Firebase
  const [salonData, setSalonData] = useState<Salon | null>(null);
  const [formData, setFormData] = useState<Partial<Salon>>({});

  // Load salon data from Firebase
  useEffect(() => {
    const loadSalonData = async () => {
      if (!user || user.role !== 'salon_owner' || !user.salonId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const salon = await SalonService.getSalonById(user.salonId);
        if (salon) {
          setSalonData(salon);
          setFormData(salon);
        } else {
          toast.error('Salon not found');
        }
      } catch (error) {
        console.error('Error loading salon data:', error);
        toast.error('Failed to load salon data');
      } finally {
        setLoading(false);
      }
    };

    loadSalonData();
  }, [user]);

  const handleSave = async () => {
    if (!user?.salonId || !salonData || !user?.id) {
      toast.error('Unable to save salon profile');
      return;
    }

    try {
      // Use the content update workflow which triggers review
      await SalonService.updateSalonContent(user.salonId, formData, user.id);

      // Update local state
      const updatedSalon = {
        ...salonData,
        ...formData,
        lastContentUpdate: new Date().toISOString(),
        contentReviewStatus: 'pending'
      };
      setSalonData(updatedSalon);
      setIsEditing(false);

      toast.success(
        'Salon profile updated successfully! Your changes are now pending admin review.',
        { duration: 5000 }
      );
    } catch (error: any) {
      console.error('Error updating salon profile:', error);

      if (error.message?.includes('Unauthorized')) {
        toast.error('You can only update your own salon profile.');
      } else if (error.message?.includes('not found')) {
        toast.error('Salon not found. Please contact support.');
      } else {
        toast.error('Failed to update salon profile. Please try again.');
      }
    }
  };

  const handleCancel = () => {
    if (salonData) {
      setFormData(salonData);
    }
    setIsEditing(false);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleHoursChange = (day: string, field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      openingHours: {
        ...prev.openingHours,
        [day]: {
          ...prev.openingHours[day as keyof typeof prev.openingHours],
          [field]: value
        }
      }
    }));
  };

  const handleLocationSelect = (coordinates: { lat: number; lng: number }, address: string) => {
    setFormData(prev => ({
      ...prev,
      coordinates,
      address
    }));
    setIsLocationPickerOpen(false);
    toast.success('Location updated successfully!');
  };

  const days = [
    { key: 'monday', label: 'Monday' },
    { key: 'tuesday', label: 'Tuesday' },
    { key: 'wednesday', label: 'Wednesday' },
    { key: 'thursday', label: 'Thursday' },
    { key: 'friday', label: 'Friday' },
    { key: 'saturday', label: 'Saturday' },
    { key: 'sunday', label: 'Sunday' },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <InlineLoader />
      </div>
    );
  }

  if (!salonData) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-glamspot-neutral-900 mb-2">Salon Not Found</h2>
          <p className="text-glamspot-neutral-600">Unable to load salon profile data.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-glamspot-neutral-900">Salon Profile</h1>
          <p className="text-glamspot-neutral-600 mt-2">
            Manage your salon's information and settings
          </p>
        </div>
        <div className="flex items-center gap-3">
          {isEditing ? (
            <>
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button onClick={handleSave} className="bg-glamspot-primary hover:bg-glamspot-primary-dark">
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </Button>
            </>
          ) : (
            <Button onClick={() => setIsEditing(true)} className="bg-glamspot-primary hover:bg-glamspot-primary-dark">
              <Edit className="w-4 h-4 mr-2" />
              Edit Profile
            </Button>
          )}
        </div>
      </div>

      {/* Approval Status */}
      {salonData.contentReviewStatus && (
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-glamspot-neutral-900">Content Review Status</h3>
                <p className="text-sm text-glamspot-neutral-600 mt-1">
                  {salonData.contentReviewStatus === 'pending' && 'Your recent changes are pending admin review.'}
                  {salonData.contentReviewStatus === 'approved' && 'Your salon content has been approved and is live.'}
                  {salonData.contentReviewStatus === 'rejected' && 'Your salon content needs updates. Please review the feedback and make necessary changes.'}
                </p>
                {salonData.lastContentUpdate && (
                  <p className="text-xs text-glamspot-neutral-500 mt-1">
                    Last updated: {new Date(salonData.lastContentUpdate).toLocaleDateString()}
                  </p>
                )}
              </div>
              <Badge
                variant={
                  salonData.contentReviewStatus === 'approved' ? 'default' :
                  salonData.contentReviewStatus === 'pending' ? 'secondary' : 'destructive'
                }
                className={
                  salonData.contentReviewStatus === 'approved' ? 'bg-green-100 text-green-800' :
                  salonData.contentReviewStatus === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                }
              >
                {salonData.contentReviewStatus === 'approved' && 'Approved'}
                {salonData.contentReviewStatus === 'pending' && 'Pending Review'}
                {salonData.contentReviewStatus === 'rejected' && 'Needs Updates'}
              </Badge>
            </div>
            {salonData.rejectionReason && salonData.contentReviewStatus === 'rejected' && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-800">
                  <strong>Feedback:</strong> {salonData.rejectionReason}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Salon Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5 text-glamspot-primary" />
            Salon Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Badge variant={salonData.isActive ? 'default' : 'secondary'} 
                     className={salonData.isActive ? 'bg-green-100 text-green-800' : ''}>
                {salonData.isActive ? 'Active' : 'Inactive'}
              </Badge>
              <div className="flex items-center gap-2">
                <Star className="w-4 h-4 text-yellow-500 fill-current" />
                <span className="font-medium">{salonData.rating}</span>
                <span className="text-glamspot-neutral-500">({salonData.totalReviews} reviews)</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>
              Update your salon's basic details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="name">Salon Name</Label>
              <Input
                id="name"
                value={isEditing ? formData.name : salonData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                disabled={!isEditing}
              />
            </div>
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={isEditing ? formData.description : salonData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                disabled={!isEditing}
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="address">Address</Label>
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-glamspot-neutral-500" />
                <Input
                  id="address"
                  value={isEditing ? formData.address : salonData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  disabled={!isEditing}
                />
                {isEditing && (
                  <Dialog open={isLocationPickerOpen} onOpenChange={setIsLocationPickerOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm">
                        <MapPin className="w-4 h-4 mr-1" />
                        Pick Location
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Choose Salon Location</DialogTitle>
                        <DialogDescription>
                          Select your salon's exact location on the map for better visibility to customers
                        </DialogDescription>
                      </DialogHeader>
                      <LocationPicker
                        initialCoordinates={formData.coordinates}
                        onLocationSelect={handleLocationSelect}
                        onCancel={() => setIsLocationPickerOpen(false)}
                      />
                    </DialogContent>
                  </Dialog>
                )}
              </div>
              {(isEditing ? formData.coordinates : salonData.coordinates) && (
                <div className="mt-2 text-xs text-glamspot-neutral-500">
                  Coordinates: {(isEditing ? formData.coordinates : salonData.coordinates)?.lat.toFixed(4)}, {(isEditing ? formData.coordinates : salonData.coordinates)?.lng.toFixed(4)}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
            <CardDescription>
              How customers can reach you
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="phone">Phone Number</Label>
              <div className="flex items-center gap-2">
                <Phone className="w-4 h-4 text-glamspot-neutral-500" />
                <Input
                  id="phone"
                  value={isEditing ? formData.phone : salonData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  disabled={!isEditing}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="email">Email</Label>
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-glamspot-neutral-500" />
                <Input
                  id="email"
                  value={isEditing ? formData.email : salonData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  disabled={!isEditing}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="website">Website</Label>
              <div className="flex items-center gap-2">
                <Globe className="w-4 h-4 text-glamspot-neutral-500" />
                <Input
                  id="website"
                  value={isEditing ? formData.website : salonData.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  disabled={!isEditing}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Social Media */}
      <Card>
        <CardHeader>
          <CardTitle>Social Media</CardTitle>
          <CardDescription>
            Connect your social media accounts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="instagram">Instagram</Label>
              <div className="flex items-center gap-2">
                <Instagram className="w-4 h-4 text-glamspot-neutral-500" />
                <Input
                  id="instagram"
                  value={isEditing ? formData.instagram : salonData.instagram}
                  onChange={(e) => handleInputChange('instagram', e.target.value)}
                  disabled={!isEditing}
                  placeholder="@username"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="facebook">Facebook</Label>
              <div className="flex items-center gap-2">
                <Facebook className="w-4 h-4 text-glamspot-neutral-500" />
                <Input
                  id="facebook"
                  value={isEditing ? formData.facebook : salonData.facebook}
                  onChange={(e) => handleInputChange('facebook', e.target.value)}
                  disabled={!isEditing}
                  placeholder="Page name"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Opening Hours */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5 text-glamspot-primary" />
            Opening Hours
          </CardTitle>
          <CardDescription>
            Set your salon's operating hours
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {days.map(({ key, label }) => {
              const hours = isEditing ? formData.openingHours[key as keyof typeof formData.openingHours] : salonData.openingHours[key as keyof typeof salonData.openingHours];
              return (
                <div key={key} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Switch
                      checked={hours.isOpen}
                      onCheckedChange={(checked) => handleHoursChange(key, 'isOpen', checked)}
                      disabled={!isEditing}
                    />
                    <span className="font-medium w-20">{label}</span>
                  </div>
                  {hours.isOpen ? (
                    <div className="flex items-center gap-2">
                      <Input
                        type="time"
                        value={hours.open}
                        onChange={(e) => handleHoursChange(key, 'open', e.target.value)}
                        disabled={!isEditing}
                        className="w-24"
                      />
                      <span>to</span>
                      <Input
                        type="time"
                        value={hours.close}
                        onChange={(e) => handleHoursChange(key, 'close', e.target.value)}
                        disabled={!isEditing}
                        className="w-24"
                      />
                    </div>
                  ) : (
                    <span className="text-glamspot-neutral-500">Closed</span>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SalonOwnerProfile;
