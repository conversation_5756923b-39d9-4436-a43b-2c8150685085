import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Building2,
  Eye,
  Check,
  X,
  MoreHorizontal,
  Clock,
  MapPin,
  Phone,
  Mail,
  Globe,
  Instagram,
  Facebook
} from 'lucide-react';
import { toast } from '@/components/ui/sonner';
import { useAuth } from '@/contexts/AuthContext';
import { SalonService } from '@/services/salonService';
import { AdminApprovalService } from '@/services/adminApprovalService';
import { Salon } from '@/types';
import { InlineLoader } from '@/components/ui/loading-spinner';

const SalonContentReview = () => {
  const { user } = useAuth();
  const [salons, setSalons] = useState<Salon[]>([]);
  const [loading, setLoading] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedSalon, setSelectedSalon] = useState<Salon | null>(null);
  const [showReviewDialog, setShowReviewDialog] = useState(false);
  const [showRejectionDialog, setShowRejectionDialog] = useState(false);
  const [salonToReject, setSalonToReject] = useState<Salon | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');

  // Load salons pending review
  const loadSalons = async () => {
    if (!user || user.role !== 'admin') return;

    try {
      setLoading(true);
      const pendingSalons = await SalonService.getSalonsPendingReview();
      setSalons(pendingSalons);
    } catch (error) {
      console.error('Error loading salons for review:', error);
      toast.error('Failed to load salons for review');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSalons();
  }, [user]);

  const handleViewSalon = (salon: Salon) => {
    setSelectedSalon(salon);
    setShowReviewDialog(true);
  };

  const handleApproveSalon = async (salon: Salon) => {
    if (!user) {
      toast.error('User not authenticated');
      return;
    }

    setIsLoading(true);
    try {
      await AdminApprovalService.approveSalonContent(salon.id, user.id);
      toast.success(`${salon.name} content has been approved and is now live!`);
      setShowReviewDialog(false);
      setSelectedSalon(null);
      loadSalons();
    } catch (error: any) {
      console.error('Error approving salon content:', error);
      toast.error('Failed to approve salon content. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRejectSalon = (salon: Salon) => {
    setSalonToReject(salon);
    setShowRejectionDialog(true);
    setShowReviewDialog(false);
  };

  const confirmRejectSalon = async () => {
    if (!salonToReject || !rejectionReason.trim() || !user) {
      toast.error('Please provide a reason for rejection');
      return;
    }

    setIsLoading(true);
    try {
      await AdminApprovalService.rejectSalonContent(
        salonToReject.id,
        user.id,
        rejectionReason
      );
      
      toast.success(`${salonToReject.name} content has been rejected with feedback`);
      setShowRejectionDialog(false);
      setSalonToReject(null);
      setRejectionReason('');
      loadSalons();
    } catch (error: any) {
      console.error('Error rejecting salon content:', error);
      toast.error('Failed to reject salon content. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800',
    };
    
    return (
      <Badge className={variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <InlineLoader />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-glamspot-neutral-900">Salon Content Review</h1>
          <p className="text-glamspot-neutral-600 mt-2">
            Review and approve salon content updates before they go live
          </p>
        </div>
        <Badge variant="secondary" className="text-sm">
          {salons.length} pending review{salons.length !== 1 ? 's' : ''}
        </Badge>
      </div>

      {/* Salons Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5 text-glamspot-primary" />
            Salons Pending Review
          </CardTitle>
          <CardDescription>
            Salon content updates waiting for admin approval
          </CardDescription>
        </CardHeader>
        <CardContent>
          {salons.length === 0 ? (
            <div className="text-center py-8">
              <Building2 className="w-12 h-12 text-glamspot-neutral-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-glamspot-neutral-900 mb-2">No pending reviews</h3>
              <p className="text-glamspot-neutral-600">All salon content is up to date!</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Salon</TableHead>
                  <TableHead>Owner</TableHead>
                  <TableHead>Last Updated</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {salons.map((salon) => (
                  <TableRow key={salon.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-glamspot-neutral-200 rounded-lg flex items-center justify-center">
                          <Building2 className="w-5 h-5 text-glamspot-neutral-600" />
                        </div>
                        <div>
                          <div className="font-medium text-glamspot-neutral-900">{salon.name}</div>
                          <div className="text-sm text-glamspot-neutral-600 flex items-center gap-1">
                            <MapPin className="w-3 h-3" />
                            {salon.location}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div className="font-medium text-glamspot-neutral-900">Owner ID: {salon.ownerId}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm text-glamspot-neutral-600">
                        {salon.lastContentUpdate ? 
                          new Date(salon.lastContentUpdate).toLocaleDateString() :
                          new Date(salon.updatedAt).toLocaleDateString()
                        }
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(salon.contentReviewStatus || 'pending')}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" disabled={isLoading}>
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleViewSalon(salon)}>
                            <Eye className="w-4 h-4 mr-2" />
                            Review Content
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleApproveSalon(salon)}>
                            <Check className="w-4 h-4 mr-2" />
                            Quick Approve
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleRejectSalon(salon)}
                            className="text-red-600"
                          >
                            <X className="w-4 h-4 mr-2" />
                            Reject Content
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Review Dialog */}
      <Dialog open={showReviewDialog} onOpenChange={setShowReviewDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Review Salon Content</DialogTitle>
            <DialogDescription>
              Review the salon information and decide whether to approve or reject the content.
            </DialogDescription>
          </DialogHeader>
          
          {selectedSalon && (
            <div className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-glamspot-neutral-900 mb-3">Basic Information</h3>
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm font-medium text-glamspot-neutral-600">Name:</span>
                      <p className="text-glamspot-neutral-900">{selectedSalon.name}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-glamspot-neutral-600">Description:</span>
                      <p className="text-glamspot-neutral-900">{selectedSalon.description}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-glamspot-neutral-600">Address:</span>
                      <p className="text-glamspot-neutral-900">{selectedSalon.address}</p>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-semibold text-glamspot-neutral-900 mb-3">Contact Information</h3>
                  <div className="space-y-2">
                    {selectedSalon.phone && (
                      <div className="flex items-center gap-2">
                        <Phone className="w-4 h-4 text-glamspot-neutral-600" />
                        <span className="text-glamspot-neutral-900">{selectedSalon.phone}</span>
                      </div>
                    )}
                    {selectedSalon.email && (
                      <div className="flex items-center gap-2">
                        <Mail className="w-4 h-4 text-glamspot-neutral-600" />
                        <span className="text-glamspot-neutral-900">{selectedSalon.email}</span>
                      </div>
                    )}
                    {selectedSalon.website && (
                      <div className="flex items-center gap-2">
                        <Globe className="w-4 h-4 text-glamspot-neutral-600" />
                        <span className="text-glamspot-neutral-900">{selectedSalon.website}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-3 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={() => setShowReviewDialog(false)}
                  disabled={isLoading}
                >
                  Close
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => handleRejectSalon(selectedSalon)}
                  disabled={isLoading}
                >
                  <X className="w-4 h-4 mr-2" />
                  Reject Content
                </Button>
                <Button
                  onClick={() => handleApproveSalon(selectedSalon)}
                  className="bg-green-600 hover:bg-green-700"
                  disabled={isLoading}
                >
                  <Check className="w-4 h-4 mr-2" />
                  Approve Content
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Rejection Dialog */}
      <AlertDialog open={showRejectionDialog} onOpenChange={setShowRejectionDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Reject Salon Content</AlertDialogTitle>
            <AlertDialogDescription>
              Please provide a reason for rejecting this salon's content. This feedback will be sent to the salon owner.
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="rejection-reason">Rejection Reason</Label>
              <Textarea
                id="rejection-reason"
                placeholder="Please explain what needs to be updated..."
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                className="mt-1"
                rows={4}
              />
            </div>
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmRejectSalon}
              disabled={!rejectionReason.trim() || isLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              {isLoading ? 'Rejecting...' : 'Reject Content'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default SalonContentReview;
